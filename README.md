

1. ح<PERSON><PERSON> XAMPP من: https://www.apachefriends.org/
2. ثبت البرنامج مع المكونات: Apache, MySQL, PHP, phpMyAdmin
3. شغل Apache و MySQL من XAMPP Control Panel

# شغل PowerShell كمدير وانتقل لمجلد المشروع
cd "C:\Users\<USER>\Desktop\BG"

# شغل سكريبت التحميل
.\download-wordpress.ps1

1. اذهب إلى: http://localhost/phpmyadmin
2. انقر على "New" وأنشئ قاعدة بيانات باسم "wordpress_db"
3. أو استخدم الملف create-database.sql

1. انسخ ملف test-environment.php إلى C:\xampp\htdocs\
2. اذهب إلى: http://localhost/test-environment.php
3. تأكد من أن جميع الاختبارات تمر بنجاح

1. اذهب إلى: http://localhost/wordpress-site
2. اتبع خطوات التثبيت
3. استخدم المعلومات التالية:
   - اسم قاعدة البيانات: wordpress_db
   - اسم المستخدم: root
   - كلمة المرور: (فارغة)
   - خادم قاعدة البيانات: localhost

BG/
├── README.md                    # هذا الملف
├── setup-instructions.md       # دليل الإعداد التفصيلي
├── download-wordpress.ps1       # سكريبت تحميل ووردبريس
├── wp-config-template.php       # قالب ملف التكوين
├── create-database.sql          # سكريبت قاعدة البيانات
├── test-environment.php         # ملف اختبار البيئة
└── wordpress-site/              # مجلد الموقع (سيتم إنشاؤه)
    ├── wp-admin/
    ├── wp-content/
    ├── wp-includes/
    ├── wp-config.php
    └── index.php

الحل:
- تأكد من تشغيل MySQL في XAMPP
- تحقق من معلومات wp-config.php
- تأكد من وجود قاعدة البيانات wordpress_db

الحل:
- فعل وضع التصحيح في wp-config.php:
  define('WP_DEBUG', true);
- تحقق من ملفات الأخطاء في مجلد logs

الحل:
- تأكد من صلاحيات القراءة والكتابة لمجلد wordpress-site
- شغل XAMPP كمدير إذا لزم الأمر

الحل:
- تحقق من إعدادات upload_max_filesize في PHP
- تأكد من وجود مساحة كافية على القرص

// تعطيل تحرير الملفات
define('DISALLOW_FILE_EDIT', true);

// إخفاء أخطاء PHP
define('WP_DEBUG_DISPLAY', false);

// تحديد مفاتيح الأمان
// احصل عليها من: https://api.wordpress.org/secret-key/1.1/salt/
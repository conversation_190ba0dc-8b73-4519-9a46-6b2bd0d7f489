# 🚀 مشروع ووردبريس المحلي - Local WordPress Project

مرحباً بك في مشروع إعداد ووردبريس محلياً! هذا المشروع يحتوي على جميع الأدوات والملفات اللازمة لإنشاء موقع ووردبريس على جهازك المحلي.

Welcome to the Local WordPress Project! This project contains all the tools and files needed to create a WordPress site on your local machine.

## 📋 المحتويات - Contents

- `setup-instructions.md` - دليل الإعداد الشامل
- `download-wordpress.ps1` - سكريبت تحميل ووردبريس تلقائياً
- `wp-config-template.php` - قالب ملف التكوين
- `create-database.sql` - سكريبت إنشاء قاعدة البيانات
- `test-environment.php` - ملف اختبار البيئة المحلية

## 🎯 البدء السريع - Quick Start

### 1. تثبيت XAMPP
```
1. حمل XAMPP من: https://www.apachefriends.org/
2. ثبت البرنامج مع المكونات: Apache, MySQL, PHP, phpMyAdmin
3. شغل Apache و MySQL من XAMPP Control Panel
```

### 2. تحميل ووردبريس
```powershell
# شغل PowerShell كمدير وانتقل لمجلد المشروع
cd "C:\Users\<USER>\Desktop\BG"

# شغل سكريبت التحميل
.\download-wordpress.ps1
```

### 3. إنشاء قاعدة البيانات
```
1. اذهب إلى: http://localhost/phpmyadmin
2. انقر على "New" وأنشئ قاعدة بيانات باسم "wordpress_db"
3. أو استخدم الملف create-database.sql
```

### 4. اختبار البيئة
```
1. انسخ ملف test-environment.php إلى C:\xampp\htdocs\
2. اذهب إلى: http://localhost/test-environment.php
3. تأكد من أن جميع الاختبارات تمر بنجاح
```

### 5. تثبيت ووردبريس
```
1. اذهب إلى: http://localhost/wordpress-site
2. اتبع خطوات التثبيت
3. استخدم المعلومات التالية:
   - اسم قاعدة البيانات: wordpress_db
   - اسم المستخدم: root
   - كلمة المرور: (فارغة)
   - خادم قاعدة البيانات: localhost
```

## 🔧 المتطلبات - Requirements

### متطلبات النظام:
- **نظام التشغيل**: Windows 10/11
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث
- **Apache**: 2.4 أو أحدث
- **الذاكرة**: 512MB RAM على الأقل
- **المساحة**: 1GB مساحة فارغة على الأقل

### الامتدادات المطلوبة:
- ✅ mysqli
- ✅ curl
- ✅ gd
- ✅ mbstring
- ✅ xml
- ✅ zip
- ✅ json
- ✅ openssl

## 📁 هيكل المشروع - Project Structure

```
BG/
├── README.md                    # هذا الملف
├── setup-instructions.md       # دليل الإعداد التفصيلي
├── download-wordpress.ps1       # سكريبت تحميل ووردبريس
├── wp-config-template.php       # قالب ملف التكوين
├── create-database.sql          # سكريبت قاعدة البيانات
├── test-environment.php         # ملف اختبار البيئة
└── wordpress-site/              # مجلد الموقع (سيتم إنشاؤه)
    ├── wp-admin/
    ├── wp-content/
    ├── wp-includes/
    ├── wp-config.php
    └── index.php
```

## 🛠️ استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة:

#### 1. خطأ "لا يمكن الاتصال بقاعدة البيانات"
```
الحل:
- تأكد من تشغيل MySQL في XAMPP
- تحقق من معلومات wp-config.php
- تأكد من وجود قاعدة البيانات wordpress_db
```

#### 2. صفحة بيضاء فارغة
```
الحل:
- فعل وضع التصحيح في wp-config.php:
  define('WP_DEBUG', true);
- تحقق من ملفات الأخطاء في مجلد logs
```

#### 3. مشاكل الصلاحيات
```
الحل:
- تأكد من صلاحيات القراءة والكتابة لمجلد wordpress-site
- شغل XAMPP كمدير إذا لزم الأمر
```

#### 4. خطأ في تحميل الإضافات/القوالب
```
الحل:
- تحقق من إعدادات upload_max_filesize في PHP
- تأكد من وجود مساحة كافية على القرص
```

## 🎨 التخصيص - Customization

### اختيار القالب:
1. اذهب إلى لوحة التحكم > المظهر > القوالب
2. اختر قالب مناسب أو حمل قالب جديد
3. فعل القالب المختار

### الإضافات المقترحة:
- **الأمان**: Wordfence Security
- **SEO**: Yoast SEO أو RankMath
- **الأداء**: WP Rocket أو W3 Total Cache
- **النسخ الاحتياطي**: UpdraftPlus
- **النماذج**: Contact Form 7

## 🔒 الأمان - Security

### نصائح أمنية مهمة:
1. استخدم كلمات مرور قوية
2. حدث ووردبريس والإضافات بانتظام
3. استخدم إضافات الأمان
4. قم بعمل نسخ احتياطية دورية
5. لا تستخدم "admin" كاسم مستخدم

### إعدادات الأمان في wp-config.php:
```php
// تعطيل تحرير الملفات
define('DISALLOW_FILE_EDIT', true);

// إخفاء أخطاء PHP
define('WP_DEBUG_DISPLAY', false);

// تحديد مفاتيح الأمان
// احصل عليها من: https://api.wordpress.org/secret-key/1.1/salt/
```

## 📊 الأداء - Performance

### نصائح لتحسين الأداء:
1. استخدم إضافات التخزين المؤقت
2. حسن الصور قبل رفعها
3. قلل من عدد الإضافات المفعلة
4. استخدم CDN للملفات الثابتة
5. نظف قاعدة البيانات بانتظام

## 🚀 النشر - Deployment

عندما تكون جاهزاً لنشر موقعك:

1. **اختر استضافة مناسبة**
2. **احصل على نطاق (Domain)**
3. **انقل الملفات عبر FTP**
4. **صدر قاعدة البيانات واستوردها**
5. **حدث wp-config.php بمعلومات الاستضافة**
6. **حدث الروابط في قاعدة البيانات**

## 📞 الدعم - Support

إذا واجهت أي مشاكل:

1. راجع ملف `setup-instructions.md`
2. شغل `test-environment.php` للتحقق من البيئة
3. تحقق من ملفات الأخطاء
4. ابحث في مجتمع ووردبريس العربي

## 📝 الترخيص - License

هذا المشروع مفتوح المصدر ومتاح للاستخدام الحر.

---

**ملاحظة مهمة**: هذا الإعداد مخصص للتطوير المحلي فقط. لا تستخدمه في بيئة الإنتاج بدون إعدادات أمان إضافية.

**Important Note**: This setup is for local development only. Do not use in production without additional security configurations.

---

🎉 **استمتع ببناء موقعك الجديد!**
🎉 **Enjoy building your new website!**

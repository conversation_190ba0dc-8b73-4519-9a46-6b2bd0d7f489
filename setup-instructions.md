# دليل إعداد موقع ووردبريس محلياً

## الخطوة 1: تثبيت XAMPP

### تحميل XAMPP:
1. اذهب إلى الموقع الرسمي: https://www.apachefriends.org/
2. حمل النسخة المناسبة لنظام Windows
3. قم بتشغيل ملف التثبيت كمدير (Run as Administrator)

### تثبيت XAMPP:
1. اتبع خطوات التثبيت الافتراضية
2. تأكد من اختيار المكونات التالية:
   - Apache
   - MySQL
   - PHP
   - phpMyAdmin

### تشغيل XAMPP:
1. افتح XAMPP Control Panel
2. ابدأ تشغيل Apache و MySQL
3. تأكد من أن الحالة تظهر "Running" باللون الأخضر

## الخطوة 2: إنشاء مجلد المشروع

1. اذه<PERSON> <PERSON>لى مجلد XAMPP (عادة C:\xampp\htdocs)
2. أن<PERSON>ئ مجلد جديد باسم "wordpress-site"
3. هذا سيكون مجلد موقعك

## الخطوة 3: تحميل ووردبريس

### تحميل ووردبريس:
1. اذهب إلى: https://wordpress.org/download/
2. حمل أحدث نسخة من ووردبريس
3. استخرج الملفات في مجلد wordpress-site

## الخطوة 4: إنشاء قاعدة البيانات

### استخدام phpMyAdmin:
1. افتح المتصفح واذهب إلى: http://localhost/phpmyadmin
2. انقر على "New" لإنشاء قاعدة بيانات جديدة
3. اكتب اسم قاعدة البيانات: "wordpress_db"
4. اختر "utf8_general_ci" كترميز
5. انقر على "Create"

## الخطوة 5: تكوين ووردبريس

### إعداد ملف التكوين:
1. في مجلد ووردبريس، ابحث عن ملف "wp-config-sample.php"
2. انسخه وأعد تسميته إلى "wp-config.php"
3. افتح الملف وعدل المعلومات التالية:
   - DB_NAME: 'wordpress_db'
   - DB_USER: 'root'
   - DB_PASSWORD: '' (فارغ افتراضياً في XAMPP)
   - DB_HOST: 'localhost'

## الخطوة 6: تشغيل التثبيت

1. افتح المتصفح واذهب إلى: http://localhost/wordpress-site
2. اتبع خطوات التثبيت:
   - اختر اللغة العربية
   - أدخل معلومات الموقع (العنوان، اسم المستخدم، كلمة المرور، البريد الإلكتروني)
   - انقر على "تثبيت ووردبريس"

## الخطوة 7: الوصول للوحة التحكم

- الموقع الأمامي: http://localhost/wordpress-site
- لوحة التحكم: http://localhost/wordpress-site/wp-admin

## نصائح مهمة:

### الأمان:
- استخدم كلمة مرور قوية لحساب المدير
- قم بتحديث ووردبريس والإضافات بانتظام

### النسخ الاحتياطي:
- انسخ مجلد الموقع وقاعدة البيانات بانتظام
- استخدم إضافات النسخ الاحتياطي

### الأداء:
- استخدم إضافات التخزين المؤقت
- حسن الصور قبل رفعها
- اختر قالب سريع ومحسن

## الإضافات المهمة المقترحة:

### الأمان:
- Wordfence Security
- iThemes Security

### تحسين محركات البحث (SEO):
- Yoast SEO
- RankMath

### الأداء:
- WP Rocket (مدفوع)
- W3 Total Cache (مجاني)

### النسخ الاحتياطي:
- UpdraftPlus
- BackWPup

## استكشاف الأخطاء:

### مشاكل شائعة:
1. **خطأ في الاتصال بقاعدة البيانات**: تحقق من معلومات wp-config.php
2. **صفحة بيضاء**: فعل وضع التصحيح في wp-config.php
3. **مشاكل الصلاحيات**: تأكد من صلاحيات المجلدات والملفات

### تفعيل وضع التصحيح:
أضف هذا السطر في wp-config.php:
```php
define('WP_DEBUG', true);
```

## الخطوات التالية:

1. اختيار وتثبيت قالب مناسب
2. إنشاء الصفحات الأساسية (الرئيسية، من نحن، اتصل بنا)
3. إعداد القوائم والودجات
4. إضافة المحتوى
5. تحسين الموقع لمحركات البحث
6. اختبار الموقع على أجهزة مختلفة

---

**ملاحظة**: هذا الدليل مخصص للتطوير المحلي فقط. لنشر الموقع على الإنترنت، ستحتاج إلى استضافة ونطاق.

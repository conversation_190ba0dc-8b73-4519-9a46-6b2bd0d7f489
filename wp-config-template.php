<?php
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the web site, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://wordpress.org/support/article/editing-wp-config-php/
 *
 * @package WordPress
 */

// ** إعدادات قاعدة البيانات - يمكنك الحصول على هذه المعلومات من مزود الاستضافة ** //
/** اسم قاعدة البيانات لووردبريس */
define( 'DB_NAME', 'wordpress_db' );

/** اسم مستخدم قاعدة البيانات */
define( 'DB_USER', 'root' );

/** كلمة مرور قاعدة البيانات */
define( 'DB_PASSWORD', '' );

/** خادم قاعدة البيانات */
define( 'DB_HOST', 'localhost' );

/** ترميز قاعدة البيانات المستخدم في إنشاء الجداول. */
define( 'DB_CHARSET', 'utf8mb4' );

/** نوع ترتيب قاعدة البيانات. لا تغير هذا إن كنت في شك. */
define( 'DB_COLLATE', '' );

/**#@+
 * مفاتيح المصادقة الفريدة وأملاح التشفير.
 *
 * قم بتغيير هذه إلى عبارات فريدة مختلفة!
 * يمكنك توليد هذه باستخدام {@link https://api.wordpress.org/secret-key/1.1/salt/ خدمة مفاتيح WordPress.org السرية}
 * يمكنك تغيير هذه في أي وقت لإبطال جميع ملفات تعريف الارتباط الموجودة. هذا سيجبر جميع المستخدمين على تسجيل الدخول مرة أخرى.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         'ضع عبارة فريدة هنا' );
define( 'SECURE_AUTH_KEY',  'ضع عبارة فريدة هنا' );
define( 'LOGGED_IN_KEY',    'ضع عبارة فريدة هنا' );
define( 'NONCE_KEY',        'ضع عبارة فريدة هنا' );
define( 'AUTH_SALT',        'ضع عبارة فريدة هنا' );
define( 'SECURE_AUTH_SALT', 'ضع عبارة فريدة هنا' );
define( 'LOGGED_IN_SALT',   'ضع عبارة فريدة هنا' );
define( 'NONCE_SALT',       'ضع عبارة فريدة هنا' );

/**#@-*/

/**
 * بادئة جداول قاعدة البيانات لووردبريس.
 *
 * يمكنك الحصول على تثبيتات متعددة في قاعدة بيانات واحدة إذا أعطيت لكل منها
 * بادئة فريدة. أرقام وحروف وشرطات سفلية فقط من فضلك!
 */
$table_prefix = 'wp_';

/**
 * للمطورين: وضع تصحيح الأخطاء في ووردبريس.
 *
 * قم بتغيير هذا إلى true لتمكين عرض الإشعارات أثناء التطوير.
 * يُنصح بشدة أن يقوم مطورو الإضافات والقوالب باستخدام WP_DEBUG
 * في بيئات التطوير الخاصة بهم.
 *
 * للحصول على معلومات حول الثوابت الأخرى التي يمكن استخدامها للتصحيح،
 * قم بزيارة الدليل.
 *
 * @link https://wordpress.org/support/article/debugging-in-wordpress/
 */
define( 'WP_DEBUG', false );

/* إعدادات إضافية للأمان والأداء */

/** تعطيل تحرير الملفات من لوحة التحكم */
define( 'DISALLOW_FILE_EDIT', true );

/** تحديد حد الذاكرة */
define( 'WP_MEMORY_LIMIT', '256M' );

/** تمكين الضغط */
define( 'WP_CACHE', true );

/** إعداد اللغة */
define( 'WPLANG', 'ar' );

/* هذا كل شيء، توقف عن التحرير! استمتع بالتدوين. */

/** المسار المطلق لمجلد ووردبريس. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** إعداد متغيرات ووردبريس وتضمين الملفات. */
require_once ABSPATH . 'wp-settings.php';

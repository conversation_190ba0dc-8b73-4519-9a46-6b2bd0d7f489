<?php
/**
 * ملف اختبار البيئة المحلية
 * Local Environment Test File
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البيئة المحلية - Local Environment Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .success {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        .error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .warning {
            border-left-color: #f39c12;
            background: #fef9e7;
        }
        .info {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }
        h1, h2 {
            margin-top: 0;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
        }
        .status.ok { background: #27ae60; }
        .status.error { background: #e74c3c; }
        .status.warning { background: #f39c12; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f5f5f5;
            font-weight: bold;
        }
        .footer {
            background: #34495e;
            color: white;
            padding: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 اختبار البيئة المحلية لووردبريس</h1>
            <h2>WordPress Local Environment Test</h2>
            <p>فحص شامل لمتطلبات تشغيل ووردبريس</p>
        </div>
        
        <div class="content">
            <div class="test-item info">
                <h3>📋 معلومات النظام - System Information</h3>
                <table>
                    <tr>
                        <th>المعلومة</th>
                        <th>القيمة</th>
                    </tr>
                    <tr>
                        <td>نظام التشغيل</td>
                        <td><?php echo php_uname('s') . ' ' . php_uname('r'); ?></td>
                    </tr>
                    <tr>
                        <td>خادم الويب</td>
                        <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></td>
                    </tr>
                    <tr>
                        <td>التاريخ والوقت</td>
                        <td><?php echo date('Y-m-d H:i:s'); ?></td>
                    </tr>
                </table>
            </div>

            <div class="test-item <?php echo version_compare(PHP_VERSION, '7.4', '>=') ? 'success' : 'error'; ?>">
                <h3>🐘 إصدار PHP - PHP Version</h3>
                <p>الإصدار الحالي: <strong><?php echo PHP_VERSION; ?></strong></p>
                <p>الحد الأدنى المطلوب: <strong>7.4</strong></p>
                <span class="status <?php echo version_compare(PHP_VERSION, '7.4', '>=') ? 'ok' : 'error'; ?>">
                    <?php echo version_compare(PHP_VERSION, '7.4', '>=') ? '✅ مناسب' : '❌ غير مناسب'; ?>
                </span>
            </div>

            <div class="test-item <?php echo extension_loaded('mysqli') ? 'success' : 'error'; ?>">
                <h3>🗄️ دعم MySQL - MySQL Support</h3>
                <p>امتداد MySQLi: 
                    <span class="status <?php echo extension_loaded('mysqli') ? 'ok' : 'error'; ?>">
                        <?php echo extension_loaded('mysqli') ? '✅ متوفر' : '❌ غير متوفر'; ?>
                    </span>
                </p>
                <?php if (extension_loaded('mysqli')): ?>
                    <p>إصدار العميل: <strong><?php echo mysqli_get_client_info(); ?></strong></p>
                <?php endif; ?>
            </div>

            <div class="test-item">
                <h3>🔧 الامتدادات المطلوبة - Required Extensions</h3>
                <?php
                $required_extensions = [
                    'curl' => 'cURL',
                    'gd' => 'GD Library',
                    'mbstring' => 'Multibyte String',
                    'xml' => 'XML Parser',
                    'zip' => 'ZIP Archive',
                    'json' => 'JSON',
                    'openssl' => 'OpenSSL'
                ];
                
                foreach ($required_extensions as $ext => $name):
                    $loaded = extension_loaded($ext);
                ?>
                    <p><?php echo $name; ?>: 
                        <span class="status <?php echo $loaded ? 'ok' : 'error'; ?>">
                            <?php echo $loaded ? '✅ متوفر' : '❌ غير متوفر'; ?>
                        </span>
                    </p>
                <?php endforeach; ?>
            </div>

            <div class="test-item">
                <h3>⚙️ إعدادات PHP - PHP Settings</h3>
                <table>
                    <tr>
                        <th>الإعداد</th>
                        <th>القيمة الحالية</th>
                        <th>المطلوب</th>
                        <th>الحالة</th>
                    </tr>
                    <?php
                    $settings = [
                        'memory_limit' => ['current' => ini_get('memory_limit'), 'required' => '128M'],
                        'max_execution_time' => ['current' => ini_get('max_execution_time'), 'required' => '30'],
                        'upload_max_filesize' => ['current' => ini_get('upload_max_filesize'), 'required' => '2M'],
                        'post_max_size' => ['current' => ini_get('post_max_size'), 'required' => '8M']
                    ];
                    
                    foreach ($settings as $setting => $values):
                    ?>
                        <tr>
                            <td><?php echo $setting; ?></td>
                            <td><?php echo $values['current']; ?></td>
                            <td><?php echo $values['required']; ?></td>
                            <td><span class="status ok">✅</span></td>
                        </tr>
                    <?php endforeach; ?>
                </table>
            </div>

            <?php
            // اختبار الاتصال بقاعدة البيانات
            $db_host = 'localhost';
            $db_user = 'root';
            $db_pass = '';
            $db_name = 'wordpress_db';
            
            $db_connection = false;
            $db_error = '';
            
            try {
                $mysqli = new mysqli($db_host, $db_user, $db_pass);
                if ($mysqli->connect_error) {
                    throw new Exception($mysqli->connect_error);
                }
                
                // التحقق من وجود قاعدة البيانات
                $result = $mysqli->query("SHOW DATABASES LIKE '$db_name'");
                $db_exists = $result && $result->num_rows > 0;
                
                $db_connection = true;
                $mysqli->close();
            } catch (Exception $e) {
                $db_error = $e->getMessage();
            }
            ?>

            <div class="test-item <?php echo $db_connection ? 'success' : 'error'; ?>">
                <h3>🔗 اتصال قاعدة البيانات - Database Connection</h3>
                <?php if ($db_connection): ?>
                    <p>الاتصال بـ MySQL: 
                        <span class="status ok">✅ نجح</span>
                    </p>
                    <p>قاعدة البيانات 'wordpress_db': 
                        <span class="status <?php echo $db_exists ? 'ok' : 'warning'; ?>">
                            <?php echo $db_exists ? '✅ موجودة' : '⚠️ غير موجودة'; ?>
                        </span>
                    </p>
                    <?php if (!$db_exists): ?>
                        <p><em>يمكنك إنشاء قاعدة البيانات من phpMyAdmin أو باستخدام الملف create-database.sql</em></p>
                    <?php endif; ?>
                <?php else: ?>
                    <p>خطأ في الاتصال: <strong><?php echo $db_error; ?></strong></p>
                    <p><span class="status error">❌ فشل الاتصال</span></p>
                <?php endif; ?>
            </div>

            <div class="test-item info">
                <h3>📁 مسارات مهمة - Important Paths</h3>
                <table>
                    <tr>
                        <th>المسار</th>
                        <th>القيمة</th>
                    </tr>
                    <tr>
                        <td>مجلد الجذر</td>
                        <td><?php echo $_SERVER['DOCUMENT_ROOT']; ?></td>
                    </tr>
                    <tr>
                        <td>المجلد الحالي</td>
                        <td><?php echo __DIR__; ?></td>
                    </tr>
                    <tr>
                        <td>مجلد PHP المؤقت</td>
                        <td><?php echo sys_get_temp_dir(); ?></td>
                    </tr>
                </table>
            </div>

            <div class="test-item success">
                <h3>🎯 الخطوات التالية - Next Steps</h3>
                <ol>
                    <li>تأكد من تشغيل Apache و MySQL في XAMPP Control Panel</li>
                    <li>أنشئ قاعدة بيانات 'wordpress_db' إذا لم تكن موجودة</li>
                    <li>حمل ووردبريس باستخدام السكريبت download-wordpress.ps1</li>
                    <li>اذهب إلى http://localhost/wordpress-site لبدء التثبيت</li>
                </ol>
            </div>
        </div>
        
        <div class="footer">
            <p>🚀 تم إنشاء هذا الاختبار لمساعدتك في إعداد ووردبريس محلياً</p>
            <p>Created to help you set up WordPress locally</p>
        </div>
    </div>
</body>
</html>

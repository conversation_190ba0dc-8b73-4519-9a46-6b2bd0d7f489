@echo off
chcp 65001 >nul
echo ================================
echo    تحميل ووردبريس تلقائياً
echo    WordPress Auto Download
echo ================================
echo.

REM التحقق من وجود PowerShell
powershell -Command "Write-Host 'PowerShell متوفر - PowerShell Available'" 2>nul
if errorlevel 1 (
    echo خطأ: PowerShell غير متوفر
    echo Error: PowerShell not available
    pause
    exit /b 1
)

echo جاري تحميل ووردبريس...
echo Downloading WordPress...
echo.

REM تحميل ووردبريس
powershell -Command "try { Invoke-WebRequest -Uri 'https://wordpress.org/latest.zip' -OutFile 'wordpress.zip' -UseBasicParsing; Write-Host 'تم التحميل بنجاح - Download successful' -ForegroundColor Green } catch { Write-Host 'خطأ في التحميل - Download error:' $_.Exception.Message -ForegroundColor Red }"

REM التحقق من وجود الملف
if exist wordpress.zip (
    echo.
    echo تم تحميل ووردبريس بنجاح!
    echo WordPress downloaded successfully!
    echo.
    
    echo جاري استخراج الملفات...
    echo Extracting files...
    
    REM إنشاء مجلد wordpress-site
    if not exist wordpress-site mkdir wordpress-site
    
    REM استخراج الملفات
    powershell -Command "try { Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('wordpress.zip', '.'); Write-Host 'تم الاستخراج بنجاح - Extraction successful' -ForegroundColor Green } catch { Write-Host 'خطأ في الاستخراج - Extraction error:' $_.Exception.Message -ForegroundColor Red }"
    
    REM نقل الملفات من مجلد wordpress إلى wordpress-site
    if exist wordpress (
        echo نقل الملفات...
        echo Moving files...
        xcopy wordpress\* wordpress-site\ /E /H /Y >nul
        rmdir wordpress /S /Q >nul
    )
    
    REM حذف ملف ZIP
    del wordpress.zip >nul
    
    REM نسخ ملف التكوين
    if exist wp-config-template.php (
        copy wp-config-template.php wordpress-site\wp-config.php >nul
        echo تم نسخ ملف التكوين
        echo Configuration file copied
    )
    
    echo.
    echo ================================
    echo       تم الانتهاء بنجاح!
    echo       Setup Complete!
    echo ================================
    echo.
    echo مسار الموقع: %CD%\wordpress-site
    echo Site path: %CD%\wordpress-site
    echo.
    echo الخطوات التالية:
    echo Next steps:
    echo 1. تأكد من تشغيل XAMPP (Apache + MySQL)
    echo    Make sure XAMPP is running (Apache + MySQL)
    echo 2. انسخ مجلد wordpress-site إلى C:\xampp\htdocs\
    echo    Copy wordpress-site folder to C:\xampp\htdocs\
    echo 3. أنشئ قاعدة بيانات 'wordpress_db' في phpMyAdmin
    echo    Create 'wordpress_db' database in phpMyAdmin
    echo 4. اذهب إلى http://localhost/wordpress-site
    echo    Go to http://localhost/wordpress-site
    echo.
    
) else (
    echo.
    echo خطأ: فشل في تحميل ووردبريس
    echo Error: Failed to download WordPress
    echo.
    echo يرجى التحقق من:
    echo Please check:
    echo - اتصال الإنترنت
    echo   Internet connection
    echo - إعدادات الجدار الناري
    echo   Firewall settings
    echo.
)

echo اضغط أي مفتاح للمتابعة...
echo Press any key to continue...
pause >nul
